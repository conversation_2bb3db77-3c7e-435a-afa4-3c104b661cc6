body {
    width:  100vw;
    height: 100vh;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    margin: 0;
    padding: 2vh;
    user-select: none;
    /* background-image: url("img/bg.png"); */
    background-position: center;
    background-size: cover;
    overflow-x: hidden;
    overflow-y: hidden;
    position: relative;
    gap: 2vh;
}

::-webkit-scrollbar {
    display:none;
    margin: 0;
}

:root {
    --logo-bg: #08DCA7;
    --item-bg: rgba(0, 0, 0, 0.85);
    --item-hover-bg: #fff;

    --item-flash: rgba(220, 8, 8, 0.5);
    --item-flash-hover: rgba(220, 8, 8, 0.4);

    --button-bg: rgba(255, 255, 255, 0.75);
    --invoice-bg: rgba(0, 0, 0, 0.75);
}

@font-face {
    font-family: "Montserrat-Black";
    src: url(fonts/Montserrat-Black.ttf);
}

@font-face {
    font-family: "Montserrat-Bold";
    src: url(fonts/Montserrat-Bold.ttf);
}

@font-face {
    font-family: "Montserrat-Medium";
    src: url(fonts/Montserrat-Medium.ttf);
}

* {
    box-sizing: border-box;
    font-family: "Montserrat-Black";
}


.main-menu {
    width: 100%;
    height: 20vh;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2vh;
    padding: 1vh;
}

.mechanic-logo {
    width: 30vh;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: var(--logo-bg);
}

.mechanic-logo > img {
    object-fit: cover;
    height: 10vh;
}

.menu-items {
    width: 80%;
    height: 100%;
    display: -webkit-box;
    align-items: center;
    justify-content: flex-start;
    overflow-y: hidden;
    overflow-x: scroll;
    gap: 1vh;
}

.menu-item {
    width: 27.5vh;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1vh;
    overflow: hidden;
    background: var(--item-bg);
    position: relative;
    text-align: center;
}

.menu-item > img {
    object-fit: cover;
    width: 7vh;
}

.menu-item > span {
    font-size: 1.25vh;
    text-transform: uppercase;
    text-overflow: ellipsis;
    overflow: hidden !important;
}

.menu-item[data-active="false"] > span {
    color: #fff;
}

.menu-item[data-active="true"] > span {
    color: black;
}

.menu-item[data-active="true"] {
    background: var(--item-hover-bg);
}

.menu-item[data-active="true"] > img {
    filter: invert(1);
}

.menu-item-price {
    width: fit-content;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 1vh;
    top: 1vh;
}

.menu-item-price > span{
    font-size: 1.75vh;
    color: var(--logo-bg);
}

.menu-item-price > i {
    font-size: 1.5vh;
    color: var(--logo-bg);
}

.color-menu {
    width: 35vh;
    height: fit-content;
    position: absolute;
    top: 4vh;
    right: 4vh;
    display: flex;
    padding: 2vh;
    align-items: center;
    flex-direction: column;
    justify-content: flex-start;
    background-color: var(--item-bg);
    gap: 1vh;
}

.color-menu > span {
    color: #fff;
    font-size: 2vh;
    text-overflow: ellipsis;
    overflow: hidden;
    text-transform: uppercase;
}

.wheel {
    width: 75%;
    height: 5vh;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.wheel[data-id="openable"] {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    height: fit-content;
    gap: 2vh;
}

.wheel[data-id="openable"] > .modal > .wheel-text > span {
    font-size: 1.75vh;
}

.divider {
    width: 100%;
    height: 0.2vw;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 2vh;
}

.divider-invoice {
    width: 100%;
    height: 0.15vw;
    background: rgba(8, 220, 167, 0.5);
    border-radius: 2vh;
}

#arrow {
    color: #fff;
    font-size: 2vh;
    text-transform: uppercase;
    transition: .2s ease-out;
}

#arrow:hover {
    cursor: pointer;
    color: var(--logo-bg);
}

#arrow:active {
    transform: scale(0.9);
}

.wheel-text {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.wheel-text > span {
    color: #fff;
    font-size: 2.25vh;
    text-transform: capitalize;
    text-overflow: ellipsis;
    overflow: hidden !important;
    font-family: "Montserrat-Bold";
}

.default-colors {
    width: 100%;
    height: fit-content;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    overflow: hidden;
    gap: 1vh;
}

.default-colors > span {
    color: #fff;
    font-size: 1.5vh;
    text-transform: capitalize;
    text-overflow: ellipsis;
    overflow: hidden;
}

.color-wheel {
    width: 100%;
    height: fit-content;
    display: -webkit-box;
    align-items: center;
    justify-content: flex-start;
    overflow-x: scroll;
    overflow-y: hidden;
    gap: 1vh;
}

.color {
    width:  2.5vh;
    height: 2.5vh;
    border: .25vh solid #fff;
    border-radius: 50%;
    background-color: #08DCA7;
}

.color:hover {
    cursor: pointer;
    transform: scale(1.02);
}

.color.chamel {
    width: fit-content;
    height: fit-content;
    border: none;
    background: none;
    border-radius: 0;
}

.color.chamel > span {
    color: #fff;
    font-size: 1.5vh;
    text-transform: capitalize;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: "Montserrat-Medium";
}

.color > input {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: none;
    background: none;
}


.menu-item[data-flash="true"] {
    animation: flash .6s infinite;
}

@keyframes flash {
    0% {
        background: var(--item-flash);
    }
    50% {
        background: var(--item-flash-hover);
    }
    100% {
        background: var(--item-flash);
    }
}

#picker {
    margin-block-end: .5vh;
    margin-block-start: .5vh;
}

.modal {
    width: 100%;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
}

.buy {
    width: fit-content;
    height: fit-content;
    padding: .75vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--button-bg);
    transition: .2s ease-out;
    border-radius: .25vh;
}

.buy:hover {
    background: var(--logo-bg);
    transform: scale(1.05);
    cursor: pointer;
}

.buy:active {
    transform: scale(1.0);
}

.fitment-menu {
    width: 30vh;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    background-color: var(--item-bg);
    position: absolute;
    top: 4vh;
    right: 4vh;
    gap: 1vh;
    padding: 2vh;
}

.fitment-menu > span {
    color: #fff;
    font-size: 2vh;
    text-overflow: ellipsis;
    overflow: hidden;
    text-transform: uppercase;
}

.fitment {
    width: 100%;
    height: fit-content;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    gap: .5vh;
}

.fitment-title {
    width: 100%;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.fitment-title > span {
    color: #fff;
    font-size: 1.5vh;
    text-overflow: ellipsis;
    overflow: hidden;
    text-transform: capitalize;
    font-family: "Montserrat-Bold";
}

.fitment-modal {
    width: 100%;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.fitment-modal > span {
    color: #fff;
    font-size: 2vh;
    text-transform: capitalize;
    text-overflow: ellipsis;
    overflow: hidden !important;
    font-family: "Montserrat-Bold";
}

.eye {
    width: fit-content;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    top: 50%;
    transform: translateY(-50%);
    position: absolute;
    flex-direction: column;
    z-index: 3;
}

.eye > span {
    color: black;
    font-size: 1.5vh;
    text-transform: capitalize;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: "Montserrat-Bold";
    background: #fff;
    padding: .25vh .75vh;
    border-radius: .25vh;
    position: relative;
}

.eye > i {
    position: absolute;
    top: 1.1vh;
    font-size: 2vh;
    color: #fff;
}

.eye > img {
    object-fit: cover;
    width: 4vh;
    margin-block-start: .75vh;
}
/* 
.invoice {
    width: 30vh;
    height: 40vh;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    position: absolute;
    left: 5vh;
    top: 10vh;
    border-radius: .25vh;
    padding: 1vh;
    background: var(--invoice-bg);
}

.invoice-logo{
    width: 100%;
    height: 10vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.invoice-logo > img {
    width: 12vh;
    object-fit: cover;
}

.basket {
    width: 100%;
    height: 60%;
    overflow-x: auto;
    overflow-y: hidden;
}

.basket-item {
    width: 100%;
    height: 5vh;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: .25vh;
    background: rgba(255, 255, 255, 0.1);
    margin-block-start: .75vh;
    padding: 1vh;
    gap: 1vh;
}

.basket-item-img {
    width: fit-content;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.basket-item-img > img {
    width: 3vh;
    object-fit: cover;
}

.basket-item-labels {
    width: 17vh;
    height: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
}

.basket-item-labels > span:first-child {
    color: #fff;
    font-size: 1.25vh;
    text-transform: capitalize;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: "Montserrat-Bold";
}

.basket-item-labels > span:last-child {
    color: rgba(255, 255, 255, 0.25);
    font-size: 1vh;
    text-transform: capitalize;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: "Montserrat-Bold";
}

.basket-item-price {
    width: fit-content;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.basket-item-price > span {
    color: rgba(8, 220, 167, 0.75);
    font-size: 1.25vh;
    text-transform: capitalize;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: "Montserrat-Bold";
}

.invoice-modals {
    width: 100%;
    height: 15%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    padding: .5vh;
    gap: .5vh;
}

.total-price {
    width: 100%;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.total-price > span {
    color: #fff;
    font-size: 1.25vh;
    text-transform: capitalize;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: "Montserrat-Bold";
}

.total-price > span:last-child {
    color: rgba(8, 220, 167, 0.75);
}

.modal-buttons {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-button {
    width: 48%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: .25vh;
}

.modal-button:hover {
    background: rgba(255, 255, 255, 0.2);
    cursor: pointer;
}

.modal-button >span {
    color: #fff;
    font-size: 1.25vh;
    text-transform: uppercase;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: "Montserrat-Bold";
}

.modal {
    width: 18vh;
    border-radius: .25vh;
    height: fit-content;
    position: absolute;
    top: 50%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    padding: 1.5vh;
    gap: .75vh;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.75);
}

.modal > span {
    color: #fff;
    font-size: 1.5vh;
    text-transform: uppercase;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: "Montserrat-Bold";
}

.modal > input {
    background: rgba(255, 255, 255, 0.25);
    border: none;
    outline: none;

    text-align: center;

    width: 100%;
    height: fit-content;

    color: #fff;
    font-size: 1.75vh;
    text-transform: uppercase;
    text-overflow: ellipsis;
    overflow: hidden;
    font-family: "Montserrat-Bold";
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.modal > .modal-button {
    width: fit-content;
    height: fit-content;
    text-align: center;
    padding: .5vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: .25vh;
}

.modal > .modal-button:hover {
    background: rgba(255, 255, 255, 0.2);
    cursor: pointer;
}
 */